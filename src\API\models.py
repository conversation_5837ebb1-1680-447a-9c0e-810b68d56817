"""Modelos Pydantic para validación de datos en la API."""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


# === MODELOS PARA PROYECTOS ===

class ProjectCreateRequest(BaseModel):
    """Modelo para crear un proyecto."""
    name: str = Field(..., description="Nombre del proyecto", min_length=1, max_length=200)
    description: str = Field("", description="Descripción del proyecto", max_length=1000)
    tags: List[str] = Field(default_factory=list, description="Lista de etiquetas")


class ProjectUpdateRequest(BaseModel):
    """Modelo para actualizar un proyecto."""
    name: Optional[str] = Field(None, description="Nuevo nombre del proyecto", min_length=1, max_length=200)
    description: Optional[str] = Field(None, description="Nueva descripción del proyecto", max_length=1000)
    tags: Optional[List[str]] = Field(None, description="Nueva lista de etiquetas")


class ProjectResponse(BaseModel):
    """Modelo de respuesta para un proyecto."""
    project_id: str
    name: str
    description: str
    tags: List[str]
    created_at: str
    updated_at: str
    test_suites: Dict[str, Any] = Field(default_factory=dict)


# === MODELOS PARA SUITES DE PRUEBAS ===

class TestSuiteCreateRequest(BaseModel):
    """Modelo para crear una suite de pruebas."""
    name: str = Field(..., description="Nombre de la suite", min_length=1, max_length=200)
    description: str = Field("", description="Descripción de la suite", max_length=1000)
    tags: List[str] = Field(default_factory=list, description="Lista de etiquetas")


class TestSuiteUpdateRequest(BaseModel):
    """Modelo para actualizar una suite de pruebas."""
    name: Optional[str] = Field(None, description="Nuevo nombre de la suite", min_length=1, max_length=200)
    description: Optional[str] = Field(None, description="Nueva descripción de la suite", max_length=1000)
    tags: Optional[List[str]] = Field(None, description="Nueva lista de etiquetas")


class TestSuiteResponse(BaseModel):
    """Modelo de respuesta para una suite de pruebas."""
    suite_id: str
    name: str
    description: str
    tags: List[str]
    created_at: str
    updated_at: str
    test_cases: Dict[str, Any] = Field(default_factory=dict)


# === MODELOS PARA CASOS DE PRUEBA ===

class TestCaseCreateRequest(BaseModel):
    """Modelo para crear un caso de prueba."""
    name: str = Field(..., description="Nombre del caso de prueba", min_length=1, max_length=200)
    description: str = Field("", description="Descripción del caso de prueba", max_length=1000)
    instrucciones: str = Field("", description="Instrucciones para ejecutar la prueba", max_length=2000)
    historia_de_usuario: str = Field("", description="Historia de usuario", max_length=2000)
    gherkin: str = Field("", description="Escenario Gherkin", max_length=5000)
    url: str = Field("", description="URL para la prueba", max_length=500)
    tags: List[str] = Field(default_factory=list, description="Lista de etiquetas")


class TestCaseUpdateRequest(BaseModel):
    """Modelo para actualizar un caso de prueba."""
    name: Optional[str] = Field(None, description="Nuevo nombre del caso de prueba", min_length=1, max_length=200)
    description: Optional[str] = Field(None, description="Nueva descripción del caso de prueba", max_length=1000)
    instrucciones: Optional[str] = Field(None, description="Nuevas instrucciones para ejecutar la prueba", max_length=2000)
    historia_de_usuario: Optional[str] = Field(None, description="Nueva historia de usuario", max_length=2000)
    gherkin: Optional[str] = Field(None, description="Nuevo escenario Gherkin", max_length=5000)
    url: Optional[str] = Field(None, description="Nueva URL para la prueba", max_length=500)
    tags: Optional[List[str]] = Field(None, description="Nueva lista de etiquetas")


class TestCaseStatusUpdateRequest(BaseModel):
    """Modelo para actualizar el estado de un caso de prueba."""
    status: str = Field(..., description="Nuevo estado del caso de prueba")


class TestCaseResponse(BaseModel):
    """Modelo de respuesta para un caso de prueba."""
    test_id: str
    name: str
    description: str
    instrucciones: str
    historia_de_usuario: str
    gherkin: str
    url: str
    tags: List[str]
    created_at: str
    updated_at: str
    history_files: List[str] = Field(default_factory=list)
    status: str = "Not Executed"
    last_execution: Optional[str] = None
    code: str = ""
    framework: str = ""


# === MODELOS PARA EJECUCIÓN ===

class TestExecutionResponse(BaseModel):
    """Modelo de respuesta para la ejecución de un test."""
    success: bool
    test_id: Optional[str] = None
    test_name: Optional[str] = None
    result: Dict[str, Any] = Field(default_factory=dict)
    error: Optional[str] = None
    execution_time: Optional[str] = None


class SuiteExecutionResponse(BaseModel):
    """Modelo de respuesta para la ejecución de una suite."""
    success: bool
    suite_id: str
    suite_name: str
    total_tests: int
    passed: int
    failed: int
    results: List[TestExecutionResponse]
    execution_time: str
    error: Optional[str] = None


# === MODELOS GENÉRICOS ===

class SuccessResponse(BaseModel):
    """Modelo de respuesta genérica de éxito."""
    success: bool = True
    message: str = "Operación completada exitosamente"


class ErrorResponse(BaseModel):
    """Modelo de respuesta genérica de error."""
    success: bool = False
    error: str
    details: Optional[str] = None


class ListResponse(BaseModel):
    """Modelo de respuesta genérica para listas."""
    success: bool = True
    count: int
    items: List[Dict[str, Any]]


# === MODELOS PARA HISTORIALES ===

class TestHistoryResponse(BaseModel):
    """Modelo de respuesta para historial de pruebas."""
    test_id: str
    history_files: List[str]
    last_execution: Optional[str] = None
    status: str


# === MODELOS PARA GENERACIÓN DE CÓDIGO ===

class CodeGenerationRequest(BaseModel):
    """Modelo para solicitud de generación de código."""
    framework: str = Field(..., description="Framework para generar el código")
    gherkin_scenario: str = Field(..., description="Escenario Gherkin")
    test_history: Dict[str, Any] = Field(..., description="Historial de la prueba")


class CodeGenerationResponse(BaseModel):
    """Modelo de respuesta para generación de código."""
    success: bool = True
    framework: str
    code: str
    error: Optional[str] = None


# === MODELOS PARA GHERKIN ===

class GherkinRequest(BaseModel):
    """Modelo para solicitud de generación de escenario Gherkin."""
    instructions: str = Field(..., description="Instrucciones para la prueba")
    url: Optional[str] = Field(None, description="URL para la prueba (opcional)")
    user_story: Optional[str] = Field(None, description="Historia de usuario (opcional)")
    language: Optional[str] = Field("es", description="Idioma de respuesta ('es' o 'en')")


class GherkinResponse(BaseModel):
    """Modelo de respuesta para generación de Gherkin."""
    success: bool = True
    gherkin: str
    error: Optional[str] = None


# === MODELOS PARA SMOKE TESTS ===

class SmokeTestRequest(BaseModel):
    """Modelo para solicitud de smoke test."""
    instructions: str = Field(..., description="Instrucciones para la prueba")
    url: Optional[str] = Field(None, description="URL para la prueba (opcional)")
    user_story: Optional[str] = Field(None, description="Historia de usuario (opcional)")


class FullTestRequest(BaseModel):
    """Modelo para solicitud de test completo."""
    gherkin_scenario: str = Field(..., description="Escenario Gherkin a ejecutar")
    url: Optional[str] = Field(None, description="URL para la prueba (opcional)")


# === MODELOS PARA HISTORIAS DE USUARIO ===

class EnhanceStoryRequest(BaseModel):
    """Modelo para solicitud de mejora de historia de usuario."""
    user_story: str = Field(..., description="Historia de usuario a mejorar")
    language: Optional[str] = Field("es", description="Idioma de respuesta ('es' o 'en')")


class GenerateManualTestsRequest(BaseModel):
    """Modelo para solicitud de generación de casos de prueba manuales."""
    enhanced_story: str = Field(..., description="Historia de usuario mejorada")
    language: Optional[str] = Field("es", description="Idioma de respuesta ('es' o 'en')")


class GenerateGherkinRequest(BaseModel):
    """Modelo para solicitud de generación de escenarios Gherkin desde casos manuales."""
    manual_tests: str = Field(..., description="Casos de prueba manuales")
    language: Optional[str] = Field("es", description="Idioma de respuesta ('es' o 'en')")


class SaveHistoryRequest(BaseModel):
    """Modelo para solicitud de guardado de historial en proyecto."""
    project_id: str = Field(..., description="ID del proyecto")
    suite_id: str = Field(..., description="ID de la suite de pruebas")
    test_history: Dict[str, Any] = Field(..., description="Historial de la prueba")
    name: str = Field(..., description="Nombre del caso de prueba")
    description: str = Field(..., description="Descripción del caso de prueba")
    gherkin: str = Field(..., description="Escenario Gherkin")
