"""User story enhancement prompts in multiple languages."""

from typing import Dict

prompts: Dict[str, Dict[str, str]] = {
    "user_story_enhance": {
        "en": """Your task is to improve the following user story to be clearer, more complete, and follow the standard format 'As a [role], I want [functionality] to [benefit]'.

Make sure to include:
1. The specific user role (Who)
2. The desired functionality (What)
3. The expected benefit or value (Why)
4. Clear and specific acceptance criteria
5. Concrete examples if possible

Original story:
{user_story}

IMPORTANT: Provide ONLY the improved user story content. Do not include any introductory text, explanations, or markdown formatting. Return only the clean, enhanced user story text.""",

        "es": """Tu tarea es mejorar la siguiente historia de usuario para que sea más clara, completa y siga el formato estándar 'Como [rol], quiero [funcionalidad] para [beneficio]'.

Asegúrate de incluir:
1. El rol específico del usuario (Quién)
2. La funcionalidad deseada (Qué)
3. El beneficio o valor esperado (Por qué)
4. Criterios de aceptación claros y específicos
5. Ejemplos concretos si es posible

Historia original:
{user_story}

IMPORTANTE: Proporciona ÚNICAMENTE el contenido de la historia de usuario mejorada. No incluyas texto introductorio, explicaciones o formato markdown. Devuelve solo el texto limpio de la historia de usuario mejorada."""
    }
}