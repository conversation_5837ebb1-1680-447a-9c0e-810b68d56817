import os
import json
import shutil
import base64
from datetime import datetime
from typing import List, Dict, Any, Optional, Union, Tuple
from .project_manager import Project, TestSuite, TestCase


def extract_screenshots_from_json(json_file_path):
    """
    Extrae las capturas de pantalla en base64 de un archivo JSON de historial y las guarda como archivos.

    Args:
        json_file_path: Ruta al archivo JSON de historial

    Returns:
        List[str]: Lista de rutas a los archivos de capturas de pantalla
    """
    try:
        with open(json_file_path, "r") as f:
            history_data = json.load(f)

        # Crear directorio para capturas de pantalla
        test_id = os.path.basename(json_file_path).split('.')[0]
        screenshots_dir = os.path.join(os.path.dirname(json_file_path), f"{test_id}_screenshots")
        os.makedirs(screenshots_dir, exist_ok=True)

        # Extraer capturas de pantalla
        screenshot_paths = []
        screenshot_index = 0

        # Buscar capturas en la estructura del historial
        for step in history_data.get("history", []):
            # Buscar en el estado
            if "state" in step and "screenshot" in step["state"]:
                screenshot = step["state"]["screenshot"]
                if isinstance(screenshot, str) and len(screenshot) > 100:  # Probablemente es base64
                    try:
                        # Guardar imagen
                        screenshot_path = os.path.join(screenshots_dir, f"screenshot_{screenshot_index+1}.png")

                        # Verificar si es una URL de datos o solo base64
                        if screenshot.startswith("data:image"):
                            # Formato típico: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...
                            image_data = screenshot.split(",")[1]
                        else:
                            # Solo base64
                            image_data = screenshot

                        image_bytes = base64.b64decode(image_data)

                        with open(screenshot_path, "wb") as f:
                            f.write(image_bytes)

                        # Guardar ruta relativa
                        screenshot_paths.append(os.path.relpath(screenshot_path))
                        screenshot_index += 1
                    except Exception as e:
                        print(f"Error al guardar captura de pantalla {screenshot_index+1}: {str(e)}")

        # Intentar extraer capturas de pantalla directamente del JSON
        if len(screenshot_paths) == 0:
            # Buscar en cualquier parte del JSON donde pueda haber una captura de pantalla
            def extract_from_dict(data, paths=None, prefix=""):
                if paths is None:
                    paths = []

                if isinstance(data, dict):
                    for key, value in data.items():
                        if key == "screenshot" and isinstance(value, str) and len(value) > 100:
                            paths.append((prefix + "." + key, value))
                        elif isinstance(value, (dict, list)):
                            extract_from_dict(value, paths, prefix + "." + key if prefix else key)
                elif isinstance(data, list):
                    for i, item in enumerate(data):
                        extract_from_dict(item, paths, f"{prefix}[{i}]")

                return paths

            # Extraer todas las capturas de pantalla del JSON
            screenshot_entries = extract_from_dict(history_data)

            # Procesar cada captura encontrada
            for i, (path, screenshot) in enumerate(screenshot_entries):
                try:
                    # Guardar imagen
                    screenshot_path = os.path.join(screenshots_dir, f"screenshot_{i+1}.png")

                    # Verificar si es una URL de datos o solo base64
                    if isinstance(screenshot, str):
                        if screenshot.startswith("data:image"):
                            # Formato típico: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...
                            image_data = screenshot.split(",")[1]
                        else:
                            # Solo base64
                            image_data = screenshot

                        try:
                            image_bytes = base64.b64decode(image_data)

                            with open(screenshot_path, "wb") as f:
                                f.write(image_bytes)

                            # Guardar ruta relativa
                            screenshot_paths.append(os.path.relpath(screenshot_path))
                            screenshot_index += 1
                        except Exception as e:
                            print(f"Error al decodificar base64 para {path}: {str(e)}")
                except Exception as e:
                    print(f"Error al guardar captura de pantalla {i+1}: {str(e)}")

        return screenshot_paths
    except Exception as e:
        print(f"Error al extraer capturas de pantalla: {str(e)}")
        return []

def load_test_history(json_file_path):
    """
    Carga y procesa un archivo JSON de historial de pruebas para su visualización.

    Args:
        json_file_path: Ruta al archivo JSON de historial

    Returns:
        dict: Datos procesados del historial de pruebas
    """
    try:
        with open(json_file_path, "r") as f:
            history_data = json.load(f)

        # Verificar si el historial está vacío
        if not history_data.get("history") or len(history_data.get("history", [])) == 0:
            return {
                "actions": [],
                "results": [],
                "elements": [],
                "urls": [],
                "errors": ["El historial de ejecución está vacío. La prueba no se completó correctamente."],
                "screenshots": [],
                "metadata": {
                    "start_time": None,
                    "end_time": None,
                    "total_steps": 0,
                    "success": False,
                    "empty_history": True,
                    "file_path": json_file_path
                }
            }

        # Procesar los datos para visualización
        processed_data = {
            "actions": [],
            "results": [],
            "elements": [],
            "urls": [],
            "errors": [],
            "screenshots": [],
            "metadata": {
                "start_time": None,
                "end_time": None,
                "total_steps": 0,
                "success": False,
                "empty_history": False,
                "file_path": json_file_path
            }
        }

        # Extraer información de cada paso
        for step in history_data.get("history", []):
            # Extraer acciones
            if "model_output" in step and "action" in step["model_output"]:
                for action in step["model_output"]["action"]:
                    processed_data["actions"].append({
                        "type": list(action.keys())[0] if action else "unknown",
                        "details": action,
                        "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                    })

            # Extraer resultados
            if "result" in step:
                for result in step["result"]:
                    if "extracted_content" in result:
                        processed_data["results"].append({
                            "content": result["extracted_content"],
                            "is_done": result.get("is_done", False),
                            "success": result.get("success", False),
                            "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                        })

            # Extraer elementos interactuados
            if "state" in step and "interacted_element" in step["state"]:
                for element in step["state"]["interacted_element"]:
                    if element:
                        processed_data["elements"].append({
                            "tag_name": element.get("tag_name", ""),
                            "xpath": element.get("xpath", ""),
                            "attributes": element.get("attributes", {}),
                            "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                        })

            # Extraer URLs
            if "state" in step and "url" in step["state"]:
                url = step["state"]["url"]
                if url and url != "about:blank":
                    processed_data["urls"].append({
                        "url": url,
                        "title": step["state"].get("title", ""),
                        "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                    })

            # Extraer metadatos
            if "metadata" in step:
                if processed_data["metadata"]["start_time"] is None or step["metadata"].get("step_start_time", 0) < processed_data["metadata"]["start_time"]:
                    processed_data["metadata"]["start_time"] = step["metadata"].get("step_start_time", 0)

                if processed_data["metadata"]["end_time"] is None or step["metadata"].get("step_end_time", 0) > processed_data["metadata"]["end_time"]:
                    processed_data["metadata"]["end_time"] = step["metadata"].get("step_end_time", 0)

                processed_data["metadata"]["total_steps"] = max(processed_data["metadata"]["total_steps"], step["metadata"].get("step_number", 0))

        # Verificar si la prueba fue exitosa
        last_results = [r for r in processed_data["results"] if r.get("is_done", False)]
        if last_results:
            processed_data["metadata"]["success"] = last_results[-1].get("success", False)

        # Extraer y procesar capturas de pantalla
        screenshot_paths = extract_screenshots_from_json(json_file_path)
        processed_data["screenshots"] = screenshot_paths

        return processed_data

    except FileNotFoundError:
        print(f"Error: No se pudo encontrar el archivo {json_file_path}")
        return {
            "actions": [],
            "results": [],
            "elements": [],
            "urls": [],
            "errors": [f"Archivo de historial no encontrado: {json_file_path}"],
            "screenshots": [],
            "metadata": {
                "start_time": None,
                "end_time": None,
                "total_steps": 0,
                "success": False,
                "file_not_found": True,
                "file_path": json_file_path
            }
        }
    except json.JSONDecodeError as e:
        print(f"Error al decodificar JSON: {str(e)}")
        return {
            "actions": [],
            "results": [],
            "elements": [],
            "urls": [],
            "errors": [f"Error al leer el archivo de historial: {str(e)}"],
            "screenshots": [],
            "metadata": {
                "start_time": None,
                "end_time": None,
                "total_steps": 0,
                "success": False,
                "json_error": True,
                "file_path": json_file_path
            }
        }
    except Exception as e:
        print(f"Error inesperado al cargar historial: {str(e)}")
        return {
            "actions": [],
            "results": [],
            "elements": [],
            "urls": [],
            "errors": [f"Error inesperado al cargar historial: {str(e)}"],
            "screenshots": [],
            "metadata": {
                "start_time": None,
                "end_time": None,
                "total_steps": 0,
                "success": False,
                "unexpected_error": True,
                "file_path": json_file_path
            }
        }


class ProjectManagerService:
    """
    Servicio para gestionar proyectos, suites de pruebas y casos de prueba.
    """
    def __init__(self, base_dir: str = "projects"):
        self.base_dir = base_dir
        self.projects_dir = os.path.join(base_dir, "projects")
        self.ensure_directories()
        self.projects = self.load_projects()

    def ensure_directories(self) -> None:
        """Asegura que existan los directorios necesarios."""
        os.makedirs(self.base_dir, exist_ok=True)
        os.makedirs(self.projects_dir, exist_ok=True)

    def load_projects(self) -> Dict[str, Project]:
        """Carga todos los proyectos desde el sistema de archivos."""
        projects = {}

        if not os.path.exists(self.projects_dir):
            return projects

        for project_file in os.listdir(self.projects_dir):
            if project_file.endswith(".json"):
                project_path = os.path.join(self.projects_dir, project_file)
                try:
                    with open(project_path, "r") as f:
                        project_data = json.load(f)
                        project = Project.from_dict(project_data)
                        projects[project.project_id] = project
                except Exception as e:
                    print(f"Error al cargar el proyecto {project_file}: {str(e)}")

        return projects

    def save_project(self, project: Project) -> bool:
        """Guarda un proyecto en el sistema de archivos."""
        try:
            project_path = os.path.join(self.projects_dir, f"{project.project_id}.json")
            with open(project_path, "w") as f:
                json.dump(project.to_dict(), f, indent=2)
            return True
        except Exception as e:
            print(f"Error al guardar el proyecto {project.name}: {str(e)}")
            return False

    def create_project(self, name: str, description: str = "", tags: List[str] = None) -> Project:
        """Crea un nuevo proyecto."""
        project = Project(name=name, description=description, tags=tags)
        self.projects[project.project_id] = project
        self.save_project(project)
        return project

    def get_project(self, project_id: str) -> Optional[Project]:
        """Obtiene un proyecto por su ID."""
        return self.projects.get(project_id)

    def get_all_projects(self) -> List[Project]:
        """Obtiene todos los proyectos."""
        return list(self.projects.values())

    def update_project(self, project_id: str, name: str = None, description: str = None, tags: List[str] = None) -> Optional[Project]:
        """Actualiza un proyecto existente."""
        project = self.get_project(project_id)
        if not project:
            return None

        if name is not None:
            project.name = name
        if description is not None:
            project.description = description
        if tags is not None:
            project.tags = tags

        project.updated_at = datetime.now().isoformat()
        self.save_project(project)
        return project

    def delete_project(self, project_id: str) -> bool:
        """Elimina un proyecto."""
        if project_id not in self.projects:
            return False

        project_path = os.path.join(self.projects_dir, f"{project_id}.json")
        if os.path.exists(project_path):
            os.remove(project_path)

        del self.projects[project_id]
        return True

    def create_test_suite(self, project_id: str, name: str, description: str = "", tags: List[str] = None) -> Optional[TestSuite]:
        """Crea una nueva suite de pruebas en un proyecto."""
        project = self.get_project(project_id)
        if not project:
            return None

        test_suite = TestSuite(name=name, description=description, tags=tags)
        project.add_test_suite(test_suite)
        self.save_project(project)
        return test_suite

    def get_test_suite(self, project_id: str, suite_id: str) -> Optional[TestSuite]:
        """Obtiene una suite de pruebas por su ID."""
        project = self.get_project(project_id)
        if not project:
            return None

        return project.get_test_suite(suite_id)

    def update_test_suite(self, project_id: str, suite_id: str, name: str = None, description: str = None, tags: List[str] = None) -> Optional[TestSuite]:
        """Actualiza una suite de pruebas existente."""
        test_suite = self.get_test_suite(project_id, suite_id)
        if not test_suite:
            return None

        if name is not None:
            test_suite.name = name
        if description is not None:
            test_suite.description = description
        if tags is not None:
            test_suite.tags = tags

        test_suite.updated_at = datetime.now().isoformat()
        self.save_project(self.projects[project_id])
        return test_suite

    def delete_test_suite(self, project_id: str, suite_id: str) -> bool:
        """Elimina una suite de pruebas."""
        project = self.get_project(project_id)
        if not project:
            return False

        result = project.remove_test_suite(suite_id)
        if result:
            self.save_project(project)
        return result

    def create_test_case(self, project_id: str, suite_id: str, name: str, description: str = "",
                         instrucciones: str = "", historia_de_usuario: str = "",
                         gherkin: str = "", url: str = "", tags: List[str] = None) -> Optional[TestCase]:
        """Crea un nuevo caso de prueba en una suite."""
        test_suite = self.get_test_suite(project_id, suite_id)
        if not test_suite:
            return None

        test_case = TestCase(
            name=name,
            description=description,
            instrucciones=instrucciones,
            historia_de_usuario=historia_de_usuario,
            gherkin=gherkin,
            url=url,
            tags=tags
        )
        test_suite.add_test_case(test_case)
        self.save_project(self.projects[project_id])
        return test_case

    def get_test_case(self, project_id: str, suite_id: str, test_id: str) -> Optional[TestCase]:
        """Obtiene un caso de prueba por su ID."""
        test_suite = self.get_test_suite(project_id, suite_id)
        if not test_suite:
            return None

        return test_suite.get_test_case(test_id)

    def update_test_case(self, project_id: str, suite_id: str, test_id: str,
                         name: str = None, description: str = None,
                         instrucciones: str = None, historia_de_usuario: str = None,
                         gherkin: str = None, url: str = None, tags: List[str] = None) -> Optional[TestCase]:
        """Actualiza un caso de prueba existente."""
        test_case = self.get_test_case(project_id, suite_id, test_id)
        if not test_case:
            return None

        if name is not None:
            test_case.name = name
        if description is not None:
            test_case.description = description
        if instrucciones is not None:
            test_case.instrucciones = instrucciones
        if historia_de_usuario is not None:
            test_case.historia_de_usuario = historia_de_usuario
        if gherkin is not None:
            test_case.gherkin = gherkin
        if url is not None:
            test_case.url = url
        if tags is not None:
            test_case.tags = tags

        test_case.updated_at = datetime.now().isoformat()
        self.save_project(self.projects[project_id])
        return test_case

    def update_test_case_status(self, project_id: str, suite_id: str, test_id: str, status: str) -> Optional[TestCase]:
        """Actualiza el estado de un caso de prueba."""
        test_case = self.get_test_case(project_id, suite_id, test_id)
        if not test_case:
            return None

        test_case.status = status
        test_case.last_execution = datetime.now().isoformat()
        test_case.updated_at = datetime.now().isoformat()
        self.save_project(self.projects[project_id])
        return test_case

    def add_history_to_test_case(self, project_id: str, suite_id: str, test_id: str, history_path: str) -> Optional[TestCase]:
        """Agrega un archivo de historial a un caso de prueba."""
        test_case = self.get_test_case(project_id, suite_id, test_id)
        if not test_case:
            return None

        # Agregar el archivo de historial si no existe ya
        if history_path not in test_case.history_files:
            test_case.history_files.append(history_path)
            test_case.updated_at = datetime.now().isoformat()
            self.save_project(self.projects[project_id])

        return test_case

    def delete_test_case(self, project_id: str, suite_id: str, test_id: str) -> bool:
        """Elimina un caso de prueba."""
        test_suite = self.get_test_suite(project_id, suite_id)
        if not test_suite:
            return False

        result = test_suite.remove_test_case(test_id)
        if result:
            self.save_project(self.projects[project_id])
        return result


