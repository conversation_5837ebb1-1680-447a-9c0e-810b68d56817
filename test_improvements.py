#!/usr/bin/env python3
"""
Test script to verify the improvements to user story, <PERSON>herkin, and test case generation.
This script tests the new language parameter and response cleaning functionality.
"""

import os
import json
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# API base URL
BASE_URL = "http://localhost:8000"

def test_enhance_story():
    """Test user story enhancement with language parameter."""
    print("Testing user story enhancement...")
    
    # Test in Spanish
    response = requests.post(f"{BASE_URL}/api/stories/enhance", json={
        "user_story": "Quiero hacer login",
        "language": "es"
    })
    
    if response.status_code == 200:
        data = response.json()
        enhanced_story = data.get("enhanced_story", "")
        print(f"✅ Spanish enhancement successful")
        print(f"Enhanced story: {enhanced_story[:100]}...")
        
        # Check for clean output (no markdown artifacts)
        if "```" not in enhanced_story and "Aquí tienes" not in enhanced_story:
            print("✅ Response is clean (no formatting artifacts)")
        else:
            print("❌ Response contains formatting artifacts")
    else:
        print(f"❌ Spanish enhancement failed: {response.status_code}")
    
    # Test in English
    response = requests.post(f"{BASE_URL}/api/stories/enhance", json={
        "user_story": "I want to login",
        "language": "en"
    })
    
    if response.status_code == 200:
        data = response.json()
        enhanced_story = data.get("enhanced_story", "")
        print(f"✅ English enhancement successful")
        print(f"Enhanced story: {enhanced_story[:100]}...")
        
        # Check for clean output
        if "```" not in enhanced_story and "Here is" not in enhanced_story:
            print("✅ Response is clean (no formatting artifacts)")
        else:
            print("❌ Response contains formatting artifacts")
    else:
        print(f"❌ English enhancement failed: {response.status_code}")

def test_generate_manual_tests():
    """Test manual test case generation with JSON format."""
    print("\nTesting manual test case generation...")
    
    enhanced_story = """Como usuario registrado del sistema de e-commerce,
    quiero poder autenticarme en la plataforma
    para acceder a mi cuenta personal y realizar compras de manera segura.
    
    Criterios de aceptación:
    1. El usuario debe poder ingresar email y contraseña
    2. El sistema debe validar las credenciales
    3. Debe redirigir al dashboard tras login exitoso"""
    
    response = requests.post(f"{BASE_URL}/api/stories/generate-manual-tests", json={
        "enhanced_story": enhanced_story,
        "language": "es"
    })
    
    if response.status_code == 200:
        data = response.json()
        manual_tests = data.get("manual_tests", "")
        print(f"✅ Manual tests generation successful")
        
        # Check if response is JSON format
        if isinstance(manual_tests, list):
            print("✅ Response is in JSON format")
            print(f"Generated {len(manual_tests)} test cases")
            
            # Check for required fields
            if manual_tests and "instrucciones" in manual_tests[0]:
                print("✅ Test cases contain 'instrucciones' field")
            else:
                print("❌ Test cases missing 'instrucciones' field")
                
            if manual_tests and "historia_de_usuario" in manual_tests[0]:
                print("✅ Test cases contain 'historia_de_usuario' field")
            else:
                print("❌ Test cases missing 'historia_de_usuario' field")
        else:
            print("❌ Response is not in JSON format")
            print(f"Response type: {type(manual_tests)}")
    else:
        print(f"❌ Manual tests generation failed: {response.status_code}")

def test_generate_gherkin():
    """Test Gherkin generation with clean output."""
    print("\nTesting Gherkin generation...")
    
    manual_tests = json.dumps([{
        "id": "TC-001",
        "title": "Login válido",
        "preconditions": "Usuario registrado en el sistema",
        "instrucciones": "1. Navegar a /login\n2. Ingresar email válido\n3. Ingresar contraseña\n4. Hacer click en 'Iniciar Sesión'",
        "expected_results": "Usuario es redirigido al dashboard",
        "priority": "High",
        "historia_de_usuario": "Como usuario quiero autenticarme"
    }])
    
    response = requests.post(f"{BASE_URL}/api/stories/generate-gherkin", json={
        "manual_tests": manual_tests,
        "language": "es"
    })
    
    if response.status_code == 200:
        data = response.json()
        gherkin = data.get("gherkin", "")
        print(f"✅ Gherkin generation successful")
        print(f"Generated Gherkin: {gherkin[:200]}...")
        
        # Check for clean output (no markdown artifacts)
        if "```gherkin" not in gherkin and "Aquí tienes" not in gherkin:
            print("✅ Gherkin response is clean (no formatting artifacts)")
        else:
            print("❌ Gherkin response contains formatting artifacts")
            
        # Check for proper Gherkin structure
        if "Feature:" in gherkin and "Scenario:" in gherkin:
            print("✅ Gherkin has proper structure")
        else:
            print("❌ Gherkin missing proper structure")
    else:
        print(f"❌ Gherkin generation failed: {response.status_code}")

def test_gherkin_from_instructions():
    """Test Gherkin generation from instructions."""
    print("\nTesting Gherkin generation from instructions...")
    
    response = requests.post(f"{BASE_URL}/api/generate/gherkin", json={
        "instructions": "Verificar que el usuario puede hacer login",
        "url": "https://example.com/login",
        "user_story": "Como usuario quiero autenticarme",
        "language": "es"
    })
    
    if response.status_code == 200:
        data = response.json()
        gherkin = data.get("gherkin", "")
        print(f"✅ Gherkin from instructions successful")
        print(f"Generated Gherkin: {gherkin[:200]}...")
        
        # Check for clean output
        if "```gherkin" not in gherkin:
            print("✅ Gherkin response is clean")
        else:
            print("❌ Gherkin response contains formatting artifacts")
    else:
        print(f"❌ Gherkin from instructions failed: {response.status_code}")

def main():
    """Run all tests."""
    print("🧪 Testing API improvements for user stories, Gherkin, and test cases")
    print("=" * 70)
    
    try:
        test_enhance_story()
        test_generate_manual_tests()
        test_generate_gherkin()
        test_gherkin_from_instructions()
        
        print("\n" + "=" * 70)
        print("✅ All tests completed! Check the results above.")
        
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to API. Make sure the server is running on localhost:8000")
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")

if __name__ == "__main__":
    main()
