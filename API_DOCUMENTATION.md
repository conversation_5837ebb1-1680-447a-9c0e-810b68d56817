# API Documentation - QA Agent

**Versión**: 2.0.0

## Resumen

La API de QA Agent expone toda la funcionalidad de gestión de proyectos, suites de pruebas y casos de prueba, siguiendo el mismo patrón arquitectónico que se usa en la interfaz de usuario. La API está completamente modularizada y sigue principios REST.

## Arquitectura

### Patrón Implementado

La API sigue el patrón de **Separación de Responsabilidades** con:

1. **Capa de Presentación**: Endpoints REST modulares
2. **Capa de Servicio**: `TestService` con mixins especializados
3. **Capa de Datos**: `ProjectManagerService` para persistencia

### Estructura Modular

```
src/API/
├── api.py              # API principal con endpoints legacy
├── models.py           # Modelos Pydantic para validación
├── project_routes.py   # Endpoints para proyectos
├── suite_routes.py     # Endpoints para suites
└── testcase_routes.py  # Endpoints para casos de prueba

src/Core/
├── test_service.py           # Servicio principal
└── test_service_extensions.py # Mixins especializados
```

## Endpoints Disponibles

### 🏗️ Gestión de Proyectos

#### Crear Proyecto
```http
POST /api/projects/
Content-Type: application/json

{
  "name": "Mi Proyecto",
  "description": "Descripción del proyecto",
  "tags": ["web", "e2e"]
}
```

#### Listar Proyectos
```http
GET /api/projects/
```

#### Obtener Proyecto
```http
GET /api/projects/{project_id}
```

#### Actualizar Proyecto
```http
PUT /api/projects/{project_id}
Content-Type: application/json

{
  "name": "Nuevo nombre",
  "description": "Nueva descripción"
}
```

#### Eliminar Proyecto
```http
DELETE /api/projects/{project_id}
```

### 📋 Gestión de Suites de Pruebas

#### Crear Suite
```http
POST /api/projects/{project_id}/suites/
Content-Type: application/json

{
  "name": "Suite de Login",
  "description": "Pruebas de autenticación",
  "tags": ["login", "auth"]
}
```

#### Obtener Suite
```http
GET /api/projects/{project_id}/suites/{suite_id}
```

#### Actualizar Suite
```http
PUT /api/projects/{project_id}/suites/{suite_id}
Content-Type: application/json

{
  "name": "Nuevo nombre de suite"
}
```

#### Eliminar Suite
```http
DELETE /api/projects/{project_id}/suites/{suite_id}
```

#### Ejecutar Suite Completa
```http
POST /api/projects/{project_id}/suites/{suite_id}/execute
```

### 🧪 Gestión de Casos de Prueba

#### Crear Caso de Prueba
```http
POST /api/projects/{project_id}/suites/{suite_id}/tests/
Content-Type: application/json

{
  "name": "Login con credenciales válidas",
  "description": "Verificar login exitoso",
  "instrucciones": "1. Navegar a login\n2. Ingresar credenciales\n3. Hacer click en login",
  "historia_de_usuario": "Como usuario quiero poder autenticarme",
  "gherkin": "Given que estoy en la página de login\nWhen ingreso credenciales válidas\nThen debo ser redirigido al dashboard",
  "url": "https://example.com/login",
  "tags": ["smoke", "critical"]
}
```

**Respuesta**:
```json
{
  "test_id": "uuid-here",
  "name": "Login con credenciales válidas",
  "description": "Verificar login exitoso",
  "instrucciones": "1. Navegar a login\n2. Ingresar credenciales\n3. Hacer click en login",
  "historia_de_usuario": "Como usuario quiero poder autenticarme",
  "gherkin": "Given que estoy en la página de login\nWhen ingreso credenciales válidas\nThen debo ser redirigido al dashboard",
  "url": "https://example.com/login",
  "tags": ["smoke", "critical"],
  "created_at": "2024-01-01T10:00:00",
  "updated_at": "2024-01-01T10:00:00",
  "history_files": [],
  "status": "Not Executed",
  "last_execution": null,
  "code": "",
  "framework": ""
}
```

#### Obtener Caso de Prueba
```http
GET /api/projects/{project_id}/suites/{suite_id}/tests/{test_id}
```

#### Actualizar Caso de Prueba
```http
PUT /api/projects/{project_id}/suites/{suite_id}/tests/{test_id}
Content-Type: application/json

{
  "name": "Nuevo nombre",
  "instrucciones": "Nuevas instrucciones"
}
```

#### Actualizar Estado del Caso
```http
PATCH /api/projects/{project_id}/suites/{suite_id}/tests/{test_id}/status
Content-Type: application/json

{
  "status": "Passed"
}
```

#### Eliminar Caso de Prueba
```http
DELETE /api/projects/{project_id}/suites/{suite_id}/tests/{test_id}
```

#### Ejecutar Caso de Prueba
```http
POST /api/projects/{project_id}/suites/{suite_id}/tests/{test_id}/execute
```

**Respuesta**:
```json
{
  "success": true,
  "test_id": "uuid-here",
  "result": {
    "success": true,
    "test_id": "timestamp",
    "history_path": "path/to/history.json",
    "history": {
      "urls": ["https://example.com"],
      "action_names": ["navigate", "click", "type"],
      "extracted_content": ["Login successful"],
      "errors": [],
      "final_result": "Test completed successfully"
    }
  },
  "error": null
}
```

### 🔍 Estado y Monitoreo

#### Verificar Estado de la API
```http
GET /api/health
```

**Respuesta**:
```json
{
  "status": "online",
  "version": "2.0.0",
  "timestamp": "2024-01-01T10:00:00",
  "api_key_configured": true
}
```

### 🚀 Ejecución de Pruebas (Legacy - Mantiene compatibilidad)

#### Smoke Test
```http
POST /api/tests/smoke
Content-Type: application/json

{
  "instructions": "Verificar que la página principal carga correctamente",
  "url": "https://example.com",
  "user_story": "Como usuario quiero acceder a la página principal"
}
```

#### Test Completo
```http
POST /api/tests/full
Content-Type: application/json

{
  "gherkin_scenario": "Given que estoy en la página principal\nWhen hago click en el botón de login\nThen debo ver el formulario de login",
  "url": "https://example.com"
}
```

### 🛠️ Generación y Utilidades

#### Generar Gherkin
```http
POST /api/generate/gherkin
Content-Type: application/json

{
  "instructions": "Probar el login",
  "url": "https://example.com/login",
  "user_story": "Como usuario quiero autenticarme",
  "language": "es"
}
```

#### Generar Código
```http
POST /api/generate/code
Content-Type: application/json

{
  "framework": "selenium",
  "gherkin_scenario": "Given...",
  "test_history": {...}
}
```

#### Mejorar Historia de Usuario
```http
POST /api/stories/enhance
Content-Type: application/json

{
  "user_story": "Como usuario quiero hacer login",
  "language": "es"
}
```

#### Generar Casos de Prueba Manuales
```http
POST /api/stories/generate-manual-tests
Content-Type: application/json

{
  "enhanced_story": "Como usuario registrado...",
  "language": "es"
}
```

#### Generar Escenarios Gherkin desde Casos Manuales
```http
POST /api/stories/generate-gherkin
Content-Type: application/json

{
  "manual_tests": "[{\"id\": \"TC-001\", \"title\": \"...\", ...}]",
  "language": "es"
}
```



## Respuestas de la API

### Respuesta Exitosa - Proyecto
```json
{
  "project_id": "uuid-here",
  "name": "Mi Proyecto",
  "description": "Descripción del proyecto",
  "tags": ["web", "e2e"],
  "created_at": "2024-01-01T10:00:00",
  "updated_at": "2024-01-01T10:00:00",
  "test_suites": {}
}
```

### Respuesta de Lista
```json
{
  "success": true,
  "count": 2,
  "items": [
    {...},
    {...}
  ]
}
```

### Respuesta de Error
```json
{
  "success": false,
  "error": "Descripción del error",
  "details": "Detalles adicionales (opcional)"
}
```

### Respuesta de Ejecución de Suite
```json
{
  "success": true,
  "suite_id": "uuid-here",
  "suite_name": "Suite de Login",
  "total_tests": 5,
  "passed": 4,
  "failed": 1,
  "results": [
    {
      "test_id": "uuid-here",
      "test_name": "Login válido",
      "result": {
        "success": true,
        "test_id": "timestamp",
        "history_path": "path/to/history.json"
      }
    }
  ],
  "execution_time": "2024-01-01T10:00:00"
}
```

## Arquitectura de Rutas

### Organización Modular

La API está organizada en módulos especializados para evitar duplicación y mantener claridad:

- **`/api/projects/*`**: Gestión de proyectos y endpoints de listado
- **`/api/projects/{project_id}/suites/*`**: Gestión de suites de pruebas
- **`/api/projects/{project_id}/suites/{suite_id}/tests/*`**: Gestión de casos de prueba
- **`/api/tests/*`**: Endpoints legacy para compatibilidad
- **`/api/generate/*`**: Utilidades de generación
- **`/api/stories/*`**: Gestión de historias de usuario
- **`/api/health`**: Monitoreo del sistema

### Nota sobre Ejecución

Los endpoints de ejecución están disponibles en múltiples rutas para flexibilidad:
- Desde el módulo de proyectos: `/api/projects/{project_id}/suites/{suite_id}/execute`
- Desde el módulo de suites: `/api/projects/{project_id}/suites/{suite_id}/execute`
- Desde el módulo de casos: `/api/projects/{project_id}/suites/{suite_id}/tests/{test_id}/execute`

## Códigos de Estado HTTP

- `200 OK`: Operación exitosa
- `201 Created`: Recurso creado exitosamente
- `400 Bad Request`: Datos de entrada inválidos
- `404 Not Found`: Recurso no encontrado
- `500 Internal Server Error`: Error interno del servidor

## Autenticación

Actualmente la API no requiere autenticación, pero se recomienda implementar autenticación para uso en producción.

## Campos Adicionales en Respuestas

### Casos de Prueba - Campos Extendidos

Los casos de prueba incluyen campos adicionales para gestión de historial y estado:

```json
{
  "test_id": "uuid-here",
  "name": "Nombre del test",
  "description": "Descripción",
  "instrucciones": "Pasos a seguir",
  "historia_de_usuario": "Historia de usuario",
  "gherkin": "Escenario Gherkin",
  "url": "URL de prueba",
  "tags": ["tag1", "tag2"],
  "created_at": "2024-01-01T10:00:00",
  "updated_at": "2024-01-01T10:00:00",
  "history_files": ["path/to/history1.json", "path/to/history2.json"],
  "status": "Passed|Failed|Not Executed",
  "last_execution": "2024-01-01T10:00:00",
  "code": "Código generado automáticamente",
  "framework": "selenium|playwright|cypress"
}
```

### Estados de Casos de Prueba

- **`Not Executed`**: Estado inicial, no se ha ejecutado
- **`Passed`**: Ejecución exitosa
- **`Failed`**: Ejecución fallida

## Ejemplos de Uso

### Flujo Completo: Crear Proyecto → Suite → Caso → Ejecutar

```bash
# 1. Verificar estado de la API
curl -X GET "http://localhost:8000/api/health"

# 2. Crear proyecto
PROJECT_ID=$(curl -X POST "http://localhost:8000/api/projects/" \
  -H "Content-Type: application/json" \
  -d '{"name": "Proyecto Demo", "description": "Demo API", "tags": ["demo", "api"]}' \
  | jq -r '.project_id')

# 3. Crear suite
SUITE_ID=$(curl -X POST "http://localhost:8000/api/projects/$PROJECT_ID/suites/" \
  -H "Content-Type: application/json" \
  -d '{"name": "Suite Demo", "description": "Suite de demostración", "tags": ["smoke"]}' \
  | jq -r '.suite_id')

# 4. Crear caso de prueba
TEST_ID=$(curl -X POST "http://localhost:8000/api/projects/$PROJECT_ID/suites/$SUITE_ID/tests/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Demo",
    "description": "Verificar funcionalidad principal",
    "instrucciones": "1. Navegar a la página principal\n2. Verificar que carga correctamente",
    "historia_de_usuario": "Como usuario quiero acceder a la página principal",
    "url": "https://example.com",
    "tags": ["smoke", "critical"]
  }' \
  | jq -r '.test_id')

# 5. Ejecutar caso de prueba
curl -X POST "http://localhost:8000/api/projects/$PROJECT_ID/suites/$SUITE_ID/tests/$TEST_ID/execute"

# 6. Verificar estado del caso de prueba
curl -X GET "http://localhost:8000/api/projects/$PROJECT_ID/suites/$SUITE_ID/tests/$TEST_ID"

# 7. Ejecutar suite completa
curl -X POST "http://localhost:8000/api/projects/$PROJECT_ID/suites/$SUITE_ID/execute"
```

### Ejemplo con Generación de Gherkin

```bash
# 1. Generar escenario Gherkin
GHERKIN=$(curl -X POST "http://localhost:8000/api/generate/gherkin" \
  -H "Content-Type: application/json" \
  -d '{
    "instructions": "Probar login con credenciales válidas",
    "url": "https://example.com/login",
    "user_story": "Como usuario registrado quiero poder autenticarme en el sistema"
  }' \
  | jq -r '.gherkin')

# 2. Usar el Gherkin generado en un test completo
curl -X POST "http://localhost:8000/api/tests/full" \
  -H "Content-Type: application/json" \
  -d "{
    \"gherkin_scenario\": \"$GHERKIN\",
    \"url\": \"https://example.com/login\"
  }"
```

## Ventajas de la Nueva Arquitectura

1. **Modularidad**: Código organizado en módulos especializados
2. **Reutilización**: Misma lógica para UI y API
3. **Mantenibilidad**: Fácil de extender y mantener
4. **Testabilidad**: Cada componente puede probarse independientemente
5. **Escalabilidad**: Fácil agregar nuevas funcionalidades
6. **Separación de Responsabilidades**: Cada módulo tiene una responsabilidad específica
7. **Compatibilidad**: Mantiene endpoints legacy para retrocompatibilidad

## Monitoreo y Debugging

### Health Check
Use el endpoint `/api/health` para verificar:
- Estado de la API
- Versión actual
- Configuración de API key
- Timestamp del servidor

### Logs y Errores
- Todos los endpoints devuelven errores estructurados con códigos HTTP apropiados
- Los errores incluyen mensajes descriptivos y detalles cuando están disponibles
- Use el campo `success` en las respuestas para verificar el estado de las operaciones

## Próximos Pasos

1. **Autenticación y Autorización**: Implementar JWT o API keys
2. **Paginación**: Agregar paginación para listas grandes de proyectos/suites/tests
3. **Filtros y Búsqueda**: Implementar filtros por tags, estado, fechas
4. **Webhooks**: Agregar notificaciones para eventos de ejecución
5. **Rate Limiting**: Implementar límites de velocidad para proteger la API
6. **Documentación Interactiva**: Mejorar la documentación Swagger/OpenAPI
7. **Métricas**: Agregar métricas de uso y rendimiento
8. **Versionado**: Implementar versionado de API para futuras actualizaciones

## Notas de Versión

### Versión 2.0.0
- ✅ Arquitectura modular completa
- ✅ Endpoints para gestión de proyectos, suites y casos de prueba
- ✅ Ejecución de tests individuales y suites completas
- ✅ Compatibilidad con endpoints legacy
- ✅ Health check endpoint
- ✅ Gestión de estados de casos de prueba
- ✅ Historial de ejecuciones
- ✅ Generación de código automática