"""
Utility module for cleaning and formatting API responses.
Removes unwanted formatting artifacts and ensures clean output.
"""

import re
import json
from typing import Any, Dict, List


def clean_gherkin_response(response: str) -> str:
    """
    Clean Gherkin response by removing markdown code blocks and unwanted text.
    
    Args:
        response: Raw response from LLM
        
    Returns:
        str: Clean Gherkin scenario
    """
    # Remove common preambles in Spanish
    preambles_es = [
        r"Aquí tienes tu escenario Gherkin.*?:",
        r"Aquí está el escenario Gherkin.*?:",
        r"Te proporciono el escenario Gherkin.*?:",
        r"Escenario Gherkin generado.*?:",
        r"A continuación.*?escenario.*?:",
    ]
    
    # Remove common preambles in English
    preambles_en = [
        r"Here is your Gherkin scenario.*?:",
        r"Here's the Gherkin scenario.*?:",
        r"Generated Gherkin scenario.*?:",
        r"Below is the.*?scenario.*?:",
    ]
    
    cleaned = response.strip()
    
    # Remove preambles
    for pattern in preambles_es + preambles_en:
        cleaned = re.sub(pattern, "", cleaned, flags=re.IGNORECASE | re.DOTALL)
    
    # Remove markdown code blocks
    cleaned = re.sub(r"```gherkin\s*", "", cleaned)
    cleaned = re.sub(r"```\s*$", "", cleaned, flags=re.MULTILINE)
    cleaned = re.sub(r"```", "", cleaned)
    
    # Remove extra whitespace and normalize line breaks
    cleaned = re.sub(r"\n\s*\n\s*\n", "\n\n", cleaned)
    cleaned = cleaned.strip()
    
    return cleaned


def clean_user_story_response(response: str) -> str:
    """
    Clean user story response by removing unwanted formatting.
    
    Args:
        response: Raw response from LLM
        
    Returns:
        str: Clean user story
    """
    # Remove common preambles in Spanish
    preambles_es = [
        r"Aquí tienes la historia de usuario mejorada.*?:",
        r"Historia de usuario mejorada.*?:",
        r"Versión mejorada.*?:",
        r"Te proporciono.*?historia.*?:",
    ]
    
    # Remove common preambles in English
    preambles_en = [
        r"Here is the improved user story.*?:",
        r"Improved user story.*?:",
        r"Enhanced version.*?:",
        r"Here's the.*?story.*?:",
    ]
    
    cleaned = response.strip()
    
    # Remove preambles
    for pattern in preambles_es + preambles_en:
        cleaned = re.sub(pattern, "", cleaned, flags=re.IGNORECASE | re.DOTALL)
    
    # Remove markdown formatting
    cleaned = re.sub(r"```.*?```", "", cleaned, flags=re.DOTALL)
    cleaned = re.sub(r"\*\*(.*?)\*\*", r"\1", cleaned)  # Remove bold
    cleaned = re.sub(r"\*(.*?)\*", r"\1", cleaned)      # Remove italic
    
    # Clean up extra whitespace
    cleaned = re.sub(r"\n\s*\n\s*\n", "\n\n", cleaned)
    cleaned = cleaned.strip()
    
    return cleaned


def clean_manual_tests_response(response: str) -> str:
    """
    Clean manual test cases response and convert to JSON format.
    
    Args:
        response: Raw response from LLM (markdown table format)
        
    Returns:
        str: JSON string with test cases
    """
    # Remove preambles
    preambles_es = [
        r"Aquí tienes los casos de prueba.*?:",
        r"Casos de prueba manuales.*?:",
        r"Te proporciono.*?casos.*?:",
    ]
    
    preambles_en = [
        r"Here are the test cases.*?:",
        r"Manual test cases.*?:",
        r"Here's the.*?test.*?:",
    ]
    
    cleaned = response.strip()
    
    # Remove preambles
    for pattern in preambles_es + preambles_en:
        cleaned = re.sub(pattern, "", cleaned, flags=re.IGNORECASE | re.DOTALL)
    
    # Try to parse markdown table to JSON
    try:
        test_cases = parse_markdown_table_to_json(cleaned)
        return json.dumps(test_cases, ensure_ascii=False, indent=2)
    except Exception:
        # If parsing fails, return cleaned markdown
        return cleaned.strip()


def parse_markdown_table_to_json(markdown_text: str) -> List[Dict[str, Any]]:
    """
    Parse markdown table to JSON format for test cases.
    
    Args:
        markdown_text: Markdown table text
        
    Returns:
        List[Dict]: List of test case dictionaries
    """
    lines = markdown_text.strip().split('\n')
    
    # Find header line
    header_line = None
    for i, line in enumerate(lines):
        if '|' in line and ('Test Case' in line or 'Caso' in line):
            header_line = i
            break
    
    if header_line is None:
        raise ValueError("No table header found")
    
    # Extract headers
    headers = [h.strip() for h in lines[header_line].split('|') if h.strip()]
    
    # Find data rows (skip separator line)
    data_rows = []
    for i in range(header_line + 2, len(lines)):  # Skip header and separator
        line = lines[i].strip()
        if line and '|' in line:
            row_data = [cell.strip() for cell in line.split('|') if cell.strip()]
            if len(row_data) >= len(headers):
                data_rows.append(row_data[:len(headers)])
    
    # Convert to JSON format with expected fields
    test_cases = []
    for row in data_rows:
        test_case = {}
        for j, header in enumerate(headers):
            if j < len(row):
                # Map to expected field names
                if 'ID' in header.upper():
                    test_case['id'] = row[j]
                elif 'TITLE' in header.upper() or 'TÍTULO' in header.upper():
                    test_case['title'] = row[j]
                elif 'PRECONDITION' in header.upper() or 'PRECONDICIÓN' in header.upper():
                    test_case['preconditions'] = row[j]
                elif 'STEP' in header.upper() or 'PASO' in header.upper():
                    test_case['instrucciones'] = row[j]
                elif 'EXPECTED' in header.upper() or 'ESPERADO' in header.upper():
                    test_case['expected_results'] = row[j]
                elif 'PRIORITY' in header.upper() or 'PRIORIDAD' in header.upper():
                    test_case['priority'] = row[j]
                else:
                    # Use header as-is for other fields
                    test_case[header.lower().replace(' ', '_')] = row[j]
        
        # Ensure required fields exist
        if 'instrucciones' not in test_case and 'test_steps' in test_case:
            test_case['instrucciones'] = test_case['test_steps']
        
        # Add historia_de_usuario field (can be derived from title/description)
        if 'historia_de_usuario' not in test_case:
            test_case['historia_de_usuario'] = test_case.get('title', '')
        
        test_cases.append(test_case)
    
    return test_cases


def format_json_response(data: Any) -> str:
    """
    Format data as clean JSON response.
    
    Args:
        data: Data to format
        
    Returns:
        str: Clean JSON string
    """
    return json.dumps(data, ensure_ascii=False, indent=2)
