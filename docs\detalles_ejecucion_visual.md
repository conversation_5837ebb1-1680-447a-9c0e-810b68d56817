# Pantalla de Detalles de la Ejecución - Documentación Visual

## Descripción General

La pantalla de "Detalles de la Ejecución" muestra información completa sobre la ejecución de tests (Smoke Test o Full Test) organizados en pestañas interactivas. Esta funcionalidad se encuentra principalmente en:


## Estructura Visual

### 1. <PERSON>cabe<PERSON><PERSON> Principal
```html
<h3 class="glow-text">Detalles de la Ejecución</h3>
```

### 2. Sistema de Pestañas (6 pestañas principales)

#### **Pestaña 1: Resultados**
- **Estado general de la prueba**:
  ```html
  <div class="status-success">✅ Prueba completada exitosamente</div>
  <!-- O -->
  <div class="status-error">❌ Prueba fallida o incompleta</div>
  ```

- **Información de tiempo**:
  ```html
  <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
    <div style="padding: 15px; background-color: var(--card);">
      <div style="font-weight: 600;">Hora de Inicio</div>
      <div>{start_time}</div>
    </div>
    <div style="padding: 15px; background-color: var(--card);">
      <div style="font-weight: 600;">Hora de Fin</div>
      <div>{end_time}</div>
    </div>
  </div>
  ```

- **Pasos ejecutados con contenido expandible**:
  ```html
  <div style="margin-bottom: 10px; padding: 10px; border-radius: 8px;">
    <div style="font-weight: 500;">{icon} Paso {step_number}</div>
    <div id="short_content_{i}" style="font-family: monospace;">
      {contenido_corto}...
    </div>
    <div id="full_content_{i}" style="display: none;">
      {contenido_completo}
    </div>
    <button onclick="toggleContent('{i}')">Mostrar más</button>
  </div>
  ```

#### **Pestaña 2: Detalles**
- **DataFrame de acciones simplificadas**
- **Vista detallada expandible por acción**:
  ```html
  <div style="margin-bottom: 10px; padding: 10px; background-color: rgba(0,0,0,0.2);">
    <div style="font-weight: 500;">Paso {step}: {action_type}</div>
    <div id="short_{details_id}" style="font-family: monospace;">
      {detalles_cortos}
    </div>
    <div id="full_{details_id}" style="display: none;">
      {detalles_completos}
    </div>
    <button onclick="toggleContent('{details_id}')">Mostrar más</button>
  </div>
  ```

#### **Pestaña 3: Elementos**
- **DataFrame con elementos interactuados**:
  ```python
  elements_df = pd.DataFrame([
      {
          "Paso": e["step"],
          "Tipo": e["tag_name"],
          "XPath": e["xpath"],
          "Atributos": str(e["attributes"])
      } for e in history_data["elements"]
  ])
  ```

#### **Pestaña 4: URLs**
- **DataFrame con URLs visitadas**:
  ```python
  urls_df = pd.DataFrame([
      {
          "Paso": u["step"],
          "URL": u["url"],
          "Título": u["title"]
      } for u in history_data["urls"]
  ])
  ```

#### **Pestaña 5: Capturas**
- **Grid de capturas de pantalla (2 columnas)**:
  ```python
  cols_per_row = 2
  for i in range(0, num_screenshots, cols_per_row):
      cols = st.columns(cols_per_row)
      for j in range(cols_per_row):
          if i + j < num_screenshots:
              with cols[j]:
                  st.markdown(f"##### Captura {i + j + 1}")
                  st.image(screenshot_path, caption=f"Paso {i + j + 1}")
  ```

#### **Pestaña 6: Metadatos**
- **Grid de información técnica**:
  ```html
  <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
    <div style="padding: 15px; background-color: var(--card);">
      <div style="font-weight: 600;">ID de Prueba</div>
      <div>{test_id}</div>
    </div>
    <div style="padding: 15px; background-color: var(--card);">
      <div style="font-weight: 600;">Total de Pasos</div>
      <div>{total_steps}</div>
    </div>
  </div>
  ```

## Estilos CSS Utilizados

### Variables CSS (Tema Oscuro)
```css
:root {
    --background: #09090B;
    --foreground: #FFFFFF;
    --card: #18181B;
    --primary: #7C3AED;
    --muted: #27272A;
    --border: #27272A;
    --radius: 8px;
}
```

### Clases Principales
```css
.glow-text {
    color: var(--primary);
    font-weight: 600;
}

.status-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10B981;
    padding: 0.75rem 1rem;
    border-radius: var(--radius);
    font-weight: 500;
    border: 1px solid rgba(16, 185, 129, 0.2);
    text-align: center;
    margin: 1rem 0;
}

.status-error {
    background-color: rgba(239, 68, 68, 0.1);
    color: #EF4444;
    padding: 0.75rem 1rem;
    border-radius: var(--radius);
    font-weight: 500;
    border: 1px solid rgba(239, 68, 68, 0.2);
    text-align: center;
    margin: 1rem 0;
}

.tab-container {
    background-color: var(--card);
    border-radius: var(--radius);
    padding: 1.25rem;
    margin-top: 1.25rem;
    border: 1px solid var(--border);
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(5px); }
    to { opacity: 1; transform: translateY(0); }
}
```

## JavaScript para Interactividad

### Función de Expandir/Contraer Contenido
```javascript
function toggleContent(id) {
    var shortContent = document.getElementById('short_' + id);
    var fullContent = document.getElementById('full_' + id);
    var button = document.getElementById('btn_' + id);

    if (shortContent.style.display === 'none') {
        shortContent.style.display = 'block';
        fullContent.style.display = 'none';
        button.textContent = 'Mostrar más';
    } else {
        shortContent.style.display = 'none';
        fullContent.style.display = 'block';
        button.textContent = 'Mostrar menos';
    }
}
```

## Flujo de Datos

### 1. Carga de Datos
```python
# En project_manager_ui.py línea 702-707
from src.Utilities.project_manager_service import load_test_history
from src.UI.test_history_ui import display_test_history

history_data = load_test_history(selected_history)
if history_data:
    st.markdown('<h3 class="glow-text">Detalles de la Ejecución</h3>')
    display_test_history(history_data, test_case.test_id)
```

### 2. Procesamiento en load_test_history
```python
processed_data = {
    "actions": [],      # Acciones ejecutadas
    "results": [],      # Resultados de cada paso
    "elements": [],     # Elementos interactuados
    "urls": [],         # URLs visitadas
    "errors": [],       # Errores encontrados
    "screenshots": [],  # Rutas de capturas
    "metadata": {       # Metadatos de la ejecución
        "start_time": None,
        "end_time": None,
        "total_steps": 0,
        "success": False
    }
}
```

### 3. Extracción de Capturas de Pantalla
```python
# Buscar capturas en base64 y convertirlas a archivos PNG
screenshot_paths = extract_screenshots_from_json(json_file_path)
processed_data["screenshots"] = screenshot_paths
```

## Puntos de Entrada

### Desde Project Manager
```python
# src/UI/project_manager_ui.py línea 706
st.markdown('<h3 class="glow-text">Detalles de la Ejecución</h3>')
display_test_history(history_data, test_case.test_id)
```

### Desde Test History
```python
# src/UI/test_history_ui.py línea 257
display_test_history(history_data, test_id)
```

### Desde App Principal (después de ejecución)
```python
# app.py líneas 1467, 1993
st.markdown('<h3 class="glow-text">Historial de Pruebas Detallado</h3>')
display_test_history(history_data, test_id)
```

## Estructura de Archivos Relacionados

```
src/
├── UI/
│   ├── test_history_ui.py          # Función principal display_test_history
│   └── project_manager_ui.py       # Punto de entrada desde project manager
├── Utilities/
│   └── project_manager_service.py  # Procesamiento de datos (load_test_history)
└── app.py                          # Estilos CSS y puntos de entrada adicionales
```

## Consideraciones Técnicas

1. **Manejo de Contenido Largo**: Se implementa un sistema de expandir/contraer para contenido que excede 200 caracteres
2. **Capturas de Pantalla**: Se extraen de base64 en el JSON y se convierten a archivos PNG
3. **Responsividad**: Se utilizan columnas de Streamlit para layout responsive
4. **Estado de Éxito/Fallo**: Se determina basado en palabras clave en los resultados finales
5. **Animaciones**: Se incluyen animaciones CSS fade-in para mejor UX

## Ejemplo de Implementación Completa

### Código para Replicar la Pantalla

```python
import streamlit as st
import pandas as pd
import os
from src.Utilities.project_manager_service import load_test_history

def create_execution_details_screen(history_file_path, test_id=None):
    """
    Replica la pantalla completa de Detalles de la Ejecución

    Args:
        history_file_path: Ruta al archivo JSON de historial
        test_id: ID del test (opcional)
    """

    # 1. Cargar datos del historial
    history_data = load_test_history(history_file_path)

    if not history_data:
        st.error("No se pudieron cargar los datos de la ejecución.")
        return

    # 2. Encabezado principal
    st.markdown('<h3 class="glow-text">Detalles de la Ejecución</h3>',
                unsafe_allow_html=True)

    # 3. Crear sistema de pestañas
    tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
        "Resultados", "Detalles", "Elementos", "URLs", "Capturas", "Metadatos"
    ])

    # 4. Implementar cada pestaña
    with tab1:
        _render_results_tab(history_data)

    with tab2:
        _render_details_tab(history_data)

    with tab3:
        _render_elements_tab(history_data)

    with tab4:
        _render_urls_tab(history_data)

    with tab5:
        _render_screenshots_tab(history_data)

    with tab6:
        _render_metadata_tab(history_data, test_id)

def _render_results_tab(history_data):
    """Renderiza la pestaña de Resultados"""
    st.markdown('<h4 class="glow-text">Resumen de Resultados</h4>',
                unsafe_allow_html=True)

    # Estado general
    success = history_data["metadata"]["success"]
    status_class = "status-success" if success else "status-error"
    status_text = "✅ Prueba completada exitosamente" if success else "❌ Prueba fallida"

    st.markdown(f'<div class="{status_class}">{status_text}</div>',
                unsafe_allow_html=True)

    # Información de tiempo
    start_time = history_data["metadata"].get("start_time", "No disponible")
    end_time = history_data["metadata"].get("end_time", "No disponible")

    time_info_html = f"""
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 20px 0;">
        <div style="padding: 15px; background-color: var(--card); border-radius: var(--radius); border: 1px solid var(--border);">
            <div style="font-weight: 600; margin-bottom: 5px;">Hora de Inicio</div>
            <div>{start_time}</div>
        </div>
        <div style="padding: 15px; background-color: var(--card); border-radius: var(--radius); border: 1px solid var(--border);">
            <div style="font-weight: 600; margin-bottom: 5px;">Hora de Fin</div>
            <div>{end_time}</div>
        </div>
    </div>
    """
    st.markdown(time_info_html, unsafe_allow_html=True)

    # Pasos ejecutados
    st.markdown('<h5>Pasos Ejecutados</h5>', unsafe_allow_html=True)

    for i, result in enumerate(history_data["results"]):
        step = result["step"]
        content = result["content"]
        success_step = result.get("success", False)

        icon = "✅" if success_step else "ℹ️"
        is_long_content = len(content) > 200 if content else False
        content_id = f"content_{i}_{step}"

        if is_long_content:
            short_content = content[:200] + "..." if content else ""

            step_html = f"""
            <div style="margin-bottom: 10px; padding: 10px; border-radius: 8px; background-color: rgba(0,0,0,0.1); border: 1px solid var(--border);">
                <div style="font-weight: 500;">{icon} Paso {step}</div>
                <div id="short_{content_id}" style="font-family: monospace; font-size: 0.9rem; margin-top: 5px; white-space: pre-wrap;">{short_content}</div>
                <div id="full_{content_id}" style="font-family: monospace; font-size: 0.9rem; margin-top: 5px; white-space: pre-wrap; display: none;">{content}</div>
                <button onclick="toggleContent('{content_id}')" id="btn_{content_id}" style="margin-top: 5px; padding: 2px 8px; background-color: #4B5563; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">Mostrar más</button>
            </div>
            """
        else:
            step_html = f"""
            <div style="margin-bottom: 10px; padding: 10px; border-radius: 8px; background-color: rgba(0,0,0,0.1); border: 1px solid var(--border);">
                <div style="font-weight: 500;">{icon} Paso {step}</div>
                <div style="font-family: monospace; font-size: 0.9rem; margin-top: 5px; white-space: pre-wrap;">{content}</div>
            </div>
            """

        st.markdown(step_html, unsafe_allow_html=True)

def _render_details_tab(history_data):
    """Renderiza la pestaña de Detalles"""
    st.markdown('<h4 class="glow-text">Acciones Detalladas</h4>',
                unsafe_allow_html=True)

    if history_data["actions"]:
        # DataFrame simplificado
        simplified_actions = []
        for a in history_data["actions"]:
            details_str = str(a["details"])
            simplified_details = details_str[:100] + "..." if len(details_str) > 100 else details_str

            simplified_actions.append({
                "Paso": a["step"],
                "Tipo": a["type"],
                "Detalles": simplified_details
            })

        actions_df = pd.DataFrame(simplified_actions)
        st.dataframe(actions_df, use_container_width=True)

        # JavaScript para expandir/contraer
        st.markdown("""
        <script>
        function toggleContent(id) {
            var shortContent = document.getElementById('short_' + id);
            var fullContent = document.getElementById('full_' + id);
            var button = document.getElementById('btn_' + id);

            if (shortContent.style.display === 'none') {
                shortContent.style.display = 'block';
                fullContent.style.display = 'none';
                button.textContent = 'Mostrar más';
            } else {
                shortContent.style.display = 'none';
                fullContent.style.display = 'block';
                button.textContent = 'Mostrar menos';
            }
        }
        </script>
        """, unsafe_allow_html=True)

        # Vista detallada expandible
        st.markdown('<h5>Vista Detallada</h5>', unsafe_allow_html=True)

        for i, action in enumerate(history_data["actions"]):
            step = action["step"]
            action_type = action["type"]
            details = str(action["details"])

            is_long_details = len(details) > 200
            details_id = f"details_{i}_{step}"

            if is_long_details:
                short_details = details[:200] + "..."

                action_html = f"""
                <div style="margin-bottom: 10px; padding: 10px; border-radius: 8px; background-color: rgba(0,0,0,0.2); border: 1px solid var(--border);">
                    <div style="font-weight: 500;">Paso {step}: {action_type}</div>
                    <div id="short_{details_id}" style="font-family: monospace; font-size: 0.9rem; margin-top: 5px; white-space: pre-wrap;">{short_details}</div>
                    <div id="full_{details_id}" style="font-family: monospace; font-size: 0.9rem; margin-top: 5px; white-space: pre-wrap; display: none;">{details}</div>
                    <button onclick="toggleContent('{details_id}')" id="btn_{details_id}" style="margin-top: 5px; padding: 2px 8px; background-color: #4B5563; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">Mostrar más</button>
                </div>
                """
            else:
                action_html = f"""
                <div style="margin-bottom: 10px; padding: 10px; border-radius: 8px; background-color: rgba(0,0,0,0.2); border: 1px solid var(--border);">
                    <div style="font-weight: 500;">Paso {step}: {action_type}</div>
                    <div style="font-family: monospace; font-size: 0.9rem; margin-top: 5px; white-space: pre-wrap;">{details}</div>
                </div>
                """

            st.markdown(action_html, unsafe_allow_html=True)
    else:
        st.info("No se registraron acciones en esta prueba.")

def _render_elements_tab(history_data):
    """Renderiza la pestaña de Elementos"""
    st.markdown('<h4 class="glow-text">Elementos Interactuados</h4>',
                unsafe_allow_html=True)

    if history_data["elements"]:
        elements_df = pd.DataFrame([
            {
                "Paso": e["step"],
                "Tipo": e["tag_name"],
                "XPath": e["xpath"],
                "Atributos": str(e["attributes"])
            } for e in history_data["elements"]
        ])
        st.dataframe(elements_df, use_container_width=True)
    else:
        st.info("No se interactuó con elementos en esta prueba.")

def _render_urls_tab(history_data):
    """Renderiza la pestaña de URLs"""
    st.markdown('<h4 class="glow-text">URLs Visitadas</h4>',
                unsafe_allow_html=True)

    if history_data["urls"]:
        urls_df = pd.DataFrame([
            {
                "Paso": u["step"],
                "URL": u["url"],
                "Título": u["title"]
            } for u in history_data["urls"]
        ])
        st.dataframe(urls_df, use_container_width=True)
    else:
        st.info("No se visitaron URLs en esta prueba.")

def _render_screenshots_tab(history_data):
    """Renderiza la pestaña de Capturas"""
    st.markdown('<h4 class="glow-text">Capturas de Pantalla</h4>',
                unsafe_allow_html=True)

    if "screenshots" in history_data and history_data["screenshots"]:
        screenshot_paths = history_data["screenshots"]
        num_screenshots = len(screenshot_paths)
        cols_per_row = 2

        for i in range(0, num_screenshots, cols_per_row):
            cols = st.columns(cols_per_row)
            for j in range(cols_per_row):
                if i + j < num_screenshots:
                    with cols[j]:
                        screenshot_path = screenshot_paths[i + j]
                        try:
                            st.markdown(f"##### Captura {i + j + 1}")
                            st.image(screenshot_path,
                                   caption=f"Paso {i + j + 1}",
                                   use_container_width=True)
                        except Exception as e:
                            st.error(f"Error al cargar captura {i + j + 1}: {str(e)}")
    else:
        st.info("No hay capturas de pantalla disponibles.")

def _render_metadata_tab(history_data, test_id):
    """Renderiza la pestaña de Metadatos"""
    st.markdown('<h4 class="glow-text">Información Técnica</h4>',
                unsafe_allow_html=True)

    metadata_html = f"""
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
        <div style="padding: 15px; background-color: var(--card); border-radius: var(--radius); border: 1px solid var(--border);">
            <div style="font-weight: 600; margin-bottom: 5px;">ID de Prueba</div>
            <div>{test_id or "No especificado"}</div>
        </div>
        <div style="padding: 15px; background-color: var(--card); border-radius: var(--radius); border: 1px solid var(--border);">
            <div style="font-weight: 600; margin-bottom: 5px;">Total de Pasos</div>
            <div>{history_data["metadata"]["total_steps"]}</div>
        </div>
        <div style="padding: 15px; background-color: var(--card); border-radius: var(--radius); border: 1px solid var(--border);">
            <div style="font-weight: 600; margin-bottom: 5px;">Acciones Ejecutadas</div>
            <div>{len(history_data["actions"])}</div>
        </div>
        <div style="padding: 15px; background-color: var(--card); border-radius: var(--radius); border: 1px solid var(--border);">
            <div style="font-weight: 600; margin-bottom: 5px;">Elementos Interactuados</div>
            <div>{len(history_data["elements"])}</div>
        </div>
        <div style="padding: 15px; background-color: var(--card); border-radius: var(--radius); border: 1px solid var(--border);">
            <div style="font-weight: 600; margin-bottom: 5px;">URLs Visitadas</div>
            <div>{len(history_data["urls"])}</div>
        </div>
        <div style="padding: 15px; background-color: var(--card); border-radius: var(--radius); border: 1px solid var(--border);">
            <div style="font-weight: 600; margin-bottom: 5px;">Capturas de Pantalla</div>
            <div>{len(history_data.get("screenshots", []))}</div>
        </div>
    </div>
    """
    st.markdown(metadata_html, unsafe_allow_html=True)

# Ejemplo de uso
if __name__ == "__main__":
    # Para usar esta función:
    # create_execution_details_screen("path/to/history.json", "test_123")
    pass
```

## Integración con CSS

Para que la pantalla funcione correctamente, asegúrate de incluir los estilos CSS en tu aplicación Streamlit:

```python
st.markdown("""
<style>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

:root {
    --background: #09090B;
    --foreground: #FFFFFF;
    --card: #18181B;
    --primary: #7C3AED;
    --border: #27272A;
    --radius: 8px;
}

.glow-text {
    color: var(--primary);
    font-weight: 600;
}

.status-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10B981;
    padding: 0.75rem 1rem;
    border-radius: var(--radius);
    font-weight: 500;
    border: 1px solid rgba(16, 185, 129, 0.2);
    text-align: center;
    margin: 1rem 0;
}

.status-error {
    background-color: rgba(239, 68, 68, 0.1);
    color: #EF4444;
    padding: 0.75rem 1rem;
    border-radius: var(--radius);
    font-weight: 500;
    border: 1px solid rgba(239, 68, 68, 0.2);
    text-align: center;
    margin: 1rem 0;
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(5px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>
""", unsafe_allow_html=True)
```

## Notas Importantes

1. **Dependencias**: Requiere `streamlit`, `pandas`, y las utilidades del proyecto
2. **Archivos de Historial**: Deben estar en formato JSON con la estructura esperada
3. **Capturas de Pantalla**: Se procesan automáticamente desde base64 a archivos PNG
4. **Interactividad**: El JavaScript debe estar habilitado para la funcionalidad de expandir/contraer
5. **Responsive**: Se adapta automáticamente a diferentes tamaños de pantalla
