"""Rutas de API para gestión de proyectos."""

from typing import List
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse

from src.Core.test_service import TestService
from src.API.models import (
    ProjectCreateRequest,
    ProjectUpdateRequest,
    ProjectResponse,
    SuccessResponse,
    ErrorResponse,
    ListResponse
)
import os

# Router para proyectos
router = APIRouter(prefix="/api/projects", tags=["projects"])


def get_test_service():
    """Crea y devuelve una instancia del servicio de pruebas."""
    return TestService(api_key=os.environ.get("GOOGLE_API_KEY"))


@router.post("/", response_model=ProjectResponse, summary="Crear proyecto")
async def create_project(
    request: ProjectCreateRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Crea un nuevo proyecto."""
    try:
        project_data = test_service.create_project(
            name=request.name,
            description=request.description,
            tags=request.tags
        )
        return ProjectResponse(**project_data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/", response_model=ListResponse, summary="Listar proyectos")
async def get_all_projects(
    test_service: TestService = Depends(get_test_service)
):
    """Obtiene todos los proyectos."""
    try:
        projects = test_service.get_all_projects()
        return ListResponse(
            count=len(projects),
            items=projects
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{project_id}", response_model=ProjectResponse, summary="Obtener proyecto")
async def get_project(
    project_id: str,
    test_service: TestService = Depends(get_test_service)
):
    """Obtiene un proyecto por su ID."""
    try:
        project_data = test_service.get_project(project_id)
        if not project_data:
            raise HTTPException(status_code=404, detail="Proyecto no encontrado")

        return ProjectResponse(**project_data)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{project_id}", response_model=ProjectResponse, summary="Actualizar proyecto")
async def update_project(
    project_id: str,
    request: ProjectUpdateRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Actualiza un proyecto existente."""
    try:
        project_data = test_service.update_project(
            project_id=project_id,
            name=request.name,
            description=request.description,
            tags=request.tags
        )
        if not project_data:
            raise HTTPException(status_code=404, detail="Proyecto no encontrado")

        return ProjectResponse(**project_data)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{project_id}", response_model=SuccessResponse, summary="Eliminar proyecto")
async def delete_project(
    project_id: str,
    test_service: TestService = Depends(get_test_service)
):
    """Elimina un proyecto."""
    try:
        success = test_service.delete_project(project_id)
        if not success:
            raise HTTPException(status_code=404, detail="Proyecto no encontrado")

        return SuccessResponse(message="Proyecto eliminado exitosamente")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# === RUTAS PARA SUITES DE PRUEBAS ===

@router.get("/{project_id}/suites", response_model=ListResponse, summary="Listar suites del proyecto")
async def get_project_suites(
    project_id: str,
    test_service: TestService = Depends(get_test_service)
):
    """Obtiene todas las suites de pruebas de un proyecto."""
    try:
        suites = test_service.get_project_suites(project_id)
        return ListResponse(
            count=len(suites),
            items=suites
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# === RUTAS PARA CASOS DE PRUEBA ===

@router.get("/{project_id}/suites/{suite_id}/tests", response_model=ListResponse,
           summary="Listar casos de prueba de la suite")
async def get_suite_test_cases(
    project_id: str,
    suite_id: str,
    test_service: TestService = Depends(get_test_service)
):
    """Obtiene todos los casos de prueba de una suite."""
    try:
        test_cases = test_service.get_suite_test_cases(project_id, suite_id)
        return ListResponse(
            count=len(test_cases),
            items=test_cases
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# === RUTAS PARA EJECUCIÓN ===

@router.post("/{project_id}/suites/{suite_id}/execute", summary="Ejecutar suite completa")
async def execute_test_suite(
    project_id: str,
    suite_id: str,
    test_service: TestService = Depends(get_test_service)
):
    """Ejecuta todos los casos de prueba de una suite."""
    try:
        result = await test_service.execute_test_suite(project_id, suite_id)
        return JSONResponse(content=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{project_id}/suites/{suite_id}/tests/{test_id}/execute",
            summary="Ejecutar caso de prueba")
async def execute_test_case(
    project_id: str,
    suite_id: str,
    test_id: str,
    test_service: TestService = Depends(get_test_service)
):
    """Ejecuta un caso de prueba específico."""
    try:
        result = await test_service.execute_test_case(project_id, suite_id, test_id)
        return JSONResponse(content=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
